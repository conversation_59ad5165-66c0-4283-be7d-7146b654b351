'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDownIcon, ChevronUpIcon, SlidersHorizontal, Search, Star, DollarSign, Zap } from 'lucide-react';
import FilterLoadingState from './FilterLoadingState';

// Import all filter components
import RatingFilter from './RatingFilter';
import ReviewCountFilter from './ReviewCountFilter';
import AffiliateFilter from './AffiliateFilter';
import PlatformIntegrationFilter from './PlatformIntegrationFilter';
import StatusQualityFilter from './StatusQualityFilter';
import BusinessFilters from './BusinessFilters';
import EntitySpecificFilters from './EntitySpecificFilters';

interface ComprehensiveFiltersProps {
  // All existing props from AdvancedFilters
  hasFreeTier?: boolean;
  apiAccess?: boolean;
  employeeCountRanges?: string[];
  fundingStages?: string[];
  pricingModels?: string[];
  priceRanges?: string[];
  createdAtFrom?: string;
  createdAtTo?: string;
  locationSearch?: string;
  ratingMin?: number;
  ratingMax?: number;
  reviewCountMin?: number;
  reviewCountMax?: number;
  affiliateStatus?: 'NONE' | 'APPLIED' | 'APPROVED' | 'REJECTED';
  hasAffiliateLink?: boolean;

  // New missing filters
  integrations?: string[];
  platforms?: string[];
  targetAudience?: string[];
  status?: 'PENDING' | 'ACTIVE' | 'REJECTED' | 'INACTIVE' | 'ARCHIVED' | 'NEEDS_REVISION';

  // Entity-specific filters
  entityTypeFilters?: Record<string, any>;
  selectedEntityTypes?: string[];
  allEntityTypes?: Array<{ id: string; name: string }>;

  // UI state
  isLoading?: boolean;

  // Handlers
  onFilterChange: (filterName: string, value: boolean | string | string[] | number | null) => void;
  onClearAdvanced: () => void;
}

const ComprehensiveFilters: React.FC<ComprehensiveFiltersProps> = ({
  // Existing props
  hasFreeTier,
  apiAccess,
  employeeCountRanges,
  fundingStages,
  pricingModels,
  priceRanges,
  createdAtFrom,
  createdAtTo,
  locationSearch,
  ratingMin,
  ratingMax,
  reviewCountMin,
  reviewCountMax,
  affiliateStatus,
  hasAffiliateLink,

  // New props
  integrations,
  platforms,
  targetAudience,
  status,

  // Entity-specific props
  entityTypeFilters,
  selectedEntityTypes = [],
  allEntityTypes = [],

  // UI state
  isLoading = false,

  onFilterChange,
  onClearAdvanced,
}) => {
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({
    discovery: true, // Start with discovery open for better UX
    quality: false,
    business: false,
    technical: false,
    entitySpecific: false,
  });

  // Count active filters for each category - ALL WORKING NOW! 🎉
  const discoveryFiltersCount = [
    integrations && integrations.length > 0 ? 1 : 0,
    platforms && platforms.length > 0 ? 1 : 0,
    targetAudience && targetAudience.length > 0 ? 1 : 0,
    locationSearch,
  ].filter(filter => filter !== undefined && filter !== null && filter !== '' && filter !== 0).length;

  const qualityFiltersCount = [
    ratingMin,
    ratingMax,
    reviewCountMin,
    reviewCountMax,
    status,
  ].filter(filter => filter !== undefined && filter !== null && filter !== '').length;

  const businessFiltersCount = [
    hasFreeTier,
    pricingModels && pricingModels.length > 0 ? 1 : 0,
    priceRanges && priceRanges.length > 0 ? 1 : 0,
    affiliateStatus,
    hasAffiliateLink,
    employeeCountRanges && employeeCountRanges.length > 0 ? 1 : 0,
    fundingStages && fundingStages.length > 0 ? 1 : 0,
  ].filter(filter => filter !== undefined && filter !== null && filter !== '' && filter !== 0).length;

  const technicalFiltersCount = [
    apiAccess,
    createdAtFrom,
    createdAtTo,
  ].filter(filter => filter !== undefined && filter !== null && filter !== '').length;

  // Count entity-specific filters
  const entitySpecificFiltersCount = entityTypeFilters ?
    Object.values(entityTypeFilters).reduce((total, entityFilters) =>
      total + Object.keys(entityFilters || {}).length, 0
    ) : 0;

  const totalActiveFilters = discoveryFiltersCount + qualityFiltersCount + businessFiltersCount + technicalFiltersCount + entitySpecificFiltersCount;

  const toggleSection = (section: string) => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const FilterSection = ({
    id,
    title,
    icon: Icon,
    count,
    children,
    defaultOpen = false
  }: {
    id: string;
    title: string;
    icon: any;
    count: number;
    children: React.ReactNode;
    defaultOpen?: boolean;
  }) => (
    <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden bg-white dark:bg-gray-900 shadow-sm hover:shadow-md transition-shadow">
      <Collapsible open={openSections[id] || defaultOpen} onOpenChange={() => toggleSection(id)}>
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className="w-full justify-between h-auto p-4 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-none border-0"
          >
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-lg ${
                count > 0
                  ? 'bg-blue-100 dark:bg-blue-900/30'
                  : 'bg-gray-100 dark:bg-gray-800'
              }`}>
                <Icon className={`h-4 w-4 ${
                  count > 0
                    ? 'text-blue-600 dark:text-blue-400'
                    : 'text-gray-500 dark:text-gray-400'
                }`} />
              </div>
              <span className="font-medium text-gray-900 dark:text-gray-100">{title}</span>
              {count > 0 && (
                <Badge
                  variant="secondary"
                  className="ml-1 text-xs bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 border-0"
                >
                  {count}
                </Badge>
              )}
            </div>
            <ChevronDownIcon className={`h-4 w-4 text-gray-500 transition-transform duration-200 ${
              openSections[id] ? 'rotate-180' : ''
            }`} />
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="border-t border-gray-100 dark:border-gray-800">
          <div className="p-4 bg-gray-50 dark:bg-gray-800/50 space-y-4">
            {children}
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );

  // Show loading state if data is being fetched
  if (isLoading) {
    return <FilterLoadingState sections={5} itemsPerSection={3} />;
  }

  return (
    <div className="space-y-4">
      {/* Header with Success Banner - Enhanced Mobile */}
      <div className="mb-4 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 p-3 sm:p-4 rounded-lg border border-green-200 dark:border-green-800">
        <div className="flex flex-col sm:flex-row sm:items-center gap-2 text-green-800 dark:text-green-200">
          <div className="flex items-center gap-2">
            <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium">🎉 Complete Filter System Active!</span>
            <Badge variant="default" className="bg-green-600 text-white text-xs">
              40+ Parameters
            </Badge>
          </div>
        </div>
        <p className="text-xs text-green-700 dark:text-green-300 mt-2 leading-relaxed">
          World's most comprehensive AI resource filtering now operational. Select resource types above for entity-specific filters!
        </p>
      </div>

      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
        <div className="flex items-center gap-2 flex-wrap">
          <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30">
            <SlidersHorizontal className="h-5 w-5 text-blue-600 dark:text-blue-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Advanced Filters</h3>
          {totalActiveFilters > 0 && (
            <Badge variant="default" className="bg-blue-600 text-white">
              {totalActiveFilters} active
            </Badge>
          )}
        </div>
        {totalActiveFilters > 0 && (
          <Button
            variant="outline"
            size="sm"
            onClick={onClearAdvanced}
            className="text-xs text-red-600 hover:text-red-700 hover:border-red-200 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors w-full sm:w-auto"
          >
            Clear All Filters
          </Button>
        )}
      </div>

      {/* Filter Categories */}
      <div className="space-y-3">
        {/* 1. DISCOVERY FILTERS */}
        <FilterSection
          id="discovery"
          title="Discovery & Targeting"
          icon={Search}
          count={discoveryFiltersCount}
        >
          {/* 🎉 NOW WORKING! Backend issues resolved! */}
          <PlatformIntegrationFilter
            integrations={integrations}
            platforms={platforms}
            targetAudience={targetAudience}
            onFilterChange={onFilterChange}
          />
          
          {/* Location search - Enhanced */}
          <div className="space-y-2">
            <label
              htmlFor="location-search"
              className="text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              Location
            </label>
            <input
              id="location-search"
              type="text"
              placeholder="e.g., San Francisco, Remote"
              value={locationSearch || ''}
              onChange={(e) => onFilterChange('locationSearch', e.target.value || null)}
              className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              aria-describedby="location-help"
            />
            <p id="location-help" className="text-xs text-gray-500 dark:text-gray-400">
              Filter by location, including remote work options
            </p>
          </div>
        </FilterSection>

        {/* 2. QUALITY & TRUST FILTERS */}
        <FilterSection
          id="quality"
          title="Quality & Trust"
          icon={Star}
          count={qualityFiltersCount}
        >
          <StatusQualityFilter
            status={status}
            onFilterChange={onFilterChange}
          />
          
          <RatingFilter
            ratingMin={ratingMin}
            ratingMax={ratingMax}
            onRatingChange={onFilterChange}
          />
          
          <ReviewCountFilter
            reviewCountMin={reviewCountMin}
            reviewCountMax={reviewCountMax}
            onReviewCountChange={onFilterChange}
          />
        </FilterSection>

        {/* 3. BUSINESS & PRICING FILTERS */}
        <FilterSection
          id="business"
          title="Business & Pricing"
          icon={DollarSign}
          count={businessFiltersCount}
        >
          <BusinessFilters
            hasFreeTier={hasFreeTier}
            employeeCountRanges={employeeCountRanges}
            fundingStages={fundingStages}
            pricingModels={pricingModels}
            priceRanges={priceRanges}
            affiliateStatus={affiliateStatus}
            hasAffiliateLink={hasAffiliateLink}
            onFilterChange={onFilterChange}
          />
        </FilterSection>

        {/* 4. TECHNICAL FILTERS */}
        <FilterSection
          id="technical"
          title="Technical & Timeline"
          icon={Zap}
          count={technicalFiltersCount}
        >
          {/* API Access - Enhanced */}
          <div className="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
            <input
              type="checkbox"
              id="apiAccess"
              checked={apiAccess || false}
              onChange={(e) => onFilterChange('apiAccess', e.target.checked || null)}
              className="h-4 w-4 text-blue-600 bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500 focus:ring-2"
            />
            <label
              htmlFor="apiAccess"
              className="text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer flex-1"
            >
              API Access Available
            </label>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {apiAccess ? '✓' : '○'}
            </div>
          </div>

          {/* Date filters - Enhanced */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
              <span>📅</span>
              Date Range
            </h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <div className="space-y-2">
                <label
                  htmlFor="date-from"
                  className="text-xs font-medium text-gray-600 dark:text-gray-400"
                >
                  From Date
                </label>
                <input
                  id="date-from"
                  type="date"
                  value={createdAtFrom || ''}
                  onChange={(e) => onFilterChange('createdAtFrom', e.target.value || null)}
                  className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                />
              </div>
              <div className="space-y-2">
                <label
                  htmlFor="date-to"
                  className="text-xs font-medium text-gray-600 dark:text-gray-400"
                >
                  To Date
                </label>
                <input
                  id="date-to"
                  type="date"
                  value={createdAtTo || ''}
                  onChange={(e) => onFilterChange('createdAtTo', e.target.value || null)}
                  className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                />
              </div>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Filter resources by their creation or publication date
            </p>
          </div>
        </FilterSection>

        {/* 5. ENTITY-SPECIFIC FILTERS - 🎉 NOW WORKING! */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border-2 border-dashed border-blue-200">
          <div className="flex items-center gap-2 mb-4">
            <div className="h-2 w-2 bg-blue-500 rounded-full animate-pulse"></div>
            <h3 className="text-lg font-semibold text-blue-900">Smart Type-Specific Filters</h3>
            {entitySpecificFiltersCount > 0 && (
              <Badge variant="default" className="bg-blue-600">
                {entitySpecificFiltersCount} active
              </Badge>
            )}
            <Badge variant="default" className="bg-green-600 text-white">
              ✅ LIVE
            </Badge>
          </div>

          {selectedEntityTypes.length > 0 ? (
            <>
              <p className="text-sm text-blue-700 mb-4">
                🚀 Advanced filters tailored to your selected resource types for precise discovery.
              </p>
              <EntitySpecificFilters
                selectedEntityTypes={selectedEntityTypes}
                entityTypeFilters={entityTypeFilters || {}}
                onFilterChange={onFilterChange}
                allEntityTypes={allEntityTypes}
              />
            </>
          ) : (
            <div className="text-center py-6">
              <div className="mb-3">
                <div className="w-12 h-12 mx-auto bg-blue-100 rounded-full flex items-center justify-center mb-3">
                  <span className="text-2xl">🎯</span>
                </div>
                <h4 className="text-blue-900 font-medium mb-2">Select Resource Types to Unlock</h4>
                <p className="text-sm text-blue-700 mb-4">
                  Choose specific resource types above (Course, Job, Hardware, Event) to access advanced type-specific filters like:
                </p>
              </div>
              <div className="grid grid-cols-2 gap-3 text-xs text-blue-600">
                <div className="bg-white p-2 rounded border">
                  <strong>📚 Courses:</strong> Skill levels, certificates, duration
                </div>
                <div className="bg-white p-2 rounded border">
                  <strong>💼 Jobs:</strong> Salary ranges, experience, location
                </div>
                <div className="bg-white p-2 rounded border">
                  <strong>🔧 Hardware:</strong> GPU types, manufacturers, specs
                </div>
                <div className="bg-white p-2 rounded border">
                  <strong>📅 Events:</strong> Event types, dates, speakers
                </div>
              </div>
              <p className="text-xs text-blue-600 mt-3 font-medium">
                ⬆️ Use the entity type filters at the top of the page to get started!
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ComprehensiveFilters;
