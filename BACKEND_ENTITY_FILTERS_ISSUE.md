# Entity Type Filters Backend Issue

## Problem Summary
The frontend is sending `entity_type_filters` parameter to the `/entities` endpoint, but the backend is rejecting it with the error:

```
entity_type_filters.property AI Tool should not exist
```

## Current Frontend Implementation

### Structure Being Sent
```json
{
  "AI Tool": {
    "technical_levels": ["BEGINNER"],
    "pricing_models": ["FREE"]
  },
  "Course": {
    "skill_levels": ["INTERMEDIATE"],
    "certificate_available": true
  }
}
```

### API Call
```
GET /entities?entity_type_filters={"AI Tool":{"technical_levels":["BEGINNER"]}}
```

## Error Analysis

The error message `entity_type_filters.property AI Tool should not exist` suggests:

1. **Validation Issue**: The backend validation is rejecting a field called `property`
2. **Structure Corruption**: The object structure might be getting corrupted somewhere
3. **Format Mismatch**: The backend might expect a different format

## Questions for Backend Team

### 1. Expected Structure
What is the correct structure for `entity_type_filters`? Options:

**Option A: Current (entity type names as keys)**
```json
{
  "AI Tool": { "technical_levels": ["BEGINNER"] }
}
```

**Option B: Snake case keys**
```json
{
  "ai_tool": { "technical_levels": ["BEGINNER"] }
}
```

**Option C: Array format**
```json
[
  {
    "entity_type": "AI Tool",
    "filters": { "technical_levels": ["BEGINNER"] }
  }
]
```

**Option D: Nested structure**
```json
{
  "filters": {
    "AI Tool": { "technical_levels": ["BEGINNER"] }
  }
}
```

### 2. DTO Definition
Can you share the DTO/schema definition for the `entity_type_filters` parameter?

Example of what we need to see:
```typescript
// NestJS DTO example
export class GetEntitiesDto {
  @IsOptional()
  @IsObject()
  entity_type_filters?: Record<string, any>; // Or specific type
}
```

### 3. Validation Configuration
- What validation library is being used? (class-validator, Joi, etc.)
- Are there any custom validators for `entity_type_filters`?
- Is `@Allow()` or similar decorator needed for dynamic keys?

### 4. Middleware/Transformation
- Is there any middleware that transforms the request body?
- Are there any pipes that might be modifying the `entity_type_filters` object?

## Debugging Information

### Frontend Logs
The frontend is logging:
- Raw object before sending
- Stringified JSON
- Parsed-back verification

### Backend Logs Needed
Please check:
- What structure is actually received by the controller
- What the validation error details show
- Any transformation logs

## Temporary Frontend Fix

I've implemented a temporary fix that tries multiple formats:

1. **Snake case conversion**: `"AI Tool"` → `"ai_tool"`
2. **Validation and cleaning**: Removes any invalid keys
3. **Comprehensive logging**: To help debug the issue

## Expected Backend Changes

Based on typical NestJS patterns, you might need:

### 1. DTO Update
```typescript
export class GetEntitiesDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => Object)
  entity_type_filters?: Record<string, EntityTypeFilterDto>;
}
```

### 2. Allow Dynamic Keys
```typescript
@IsOptional()
@IsObject()
@Allow() // Allow any additional properties
entity_type_filters?: Record<string, any>;
```

### 3. Custom Validator
```typescript
@IsOptional()
@IsEntityTypeFilters() // Custom validator
entity_type_filters?: Record<string, any>;
```

## Next Steps

1. **Backend Team**: Please provide the expected structure and DTO definition
2. **Frontend Team**: Will adjust the format based on backend requirements
3. **Testing**: Once format is confirmed, test with all entity types
4. **Documentation**: Update API documentation with correct format

## Contact
Please respond with:
- Expected `entity_type_filters` structure
- DTO definition
- Any validation requirements
- Timeline for backend fixes (if needed)
