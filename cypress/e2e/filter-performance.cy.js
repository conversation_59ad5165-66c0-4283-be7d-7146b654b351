describe('Filter Performance E2E Tests', () => {
  beforeEach(() => {
    cy.visit('/browse');
    cy.get('[data-testid="browse-page"]', { timeout: 10000 }).should('be.visible');
  });

  describe('API Performance', () => {
    it('should handle rapid filter changes without overwhelming the API', () => {
      // Intercept API calls to monitor frequency
      let apiCallCount = 0;
      cy.intercept('GET', '/api/entities*', (req) => {
        apiCallCount++;
        req.reply({ fixture: 'entities-response.json' });
      }).as('getEntities');

      // Apply multiple filters rapidly
      cy.get('[data-testid="search-input"]').type('AI');
      cy.get('[data-testid="entity-type-option"]').first().click();
      cy.get('[data-testid="category-option"]').first().click();
      cy.get('[data-testid="tag-option"]').first().click();

      // Wait for debounce period
      cy.wait(1000);

      // Check that API calls were debounced (should be fewer than filter changes)
      cy.then(() => {
        expect(apiCallCount).to.be.lessThan(4);
      });
    });

    it('should show loading states during API calls', () => {
      // Intercept API with delay to test loading states
      cy.intercept('GET', '/api/entities*', { 
        delay: 1000,
        fixture: 'entities-response.json' 
      }).as('getEntitiesWithDelay');

      // Apply a filter
      cy.get('[data-testid="entity-type-option"]').first().click();

      // Check that loading state appears
      cy.get('[data-testid="results-loading"]').should('be.visible');
      cy.get('[data-testid="filter-loading"]').should('be.visible');

      // Wait for API call to complete
      cy.wait('@getEntitiesWithDelay');

      // Check that loading states disappear
      cy.get('[data-testid="results-loading"]').should('not.exist');
      cy.get('[data-testid="filter-loading"]').should('not.exist');
      cy.get('[data-testid="results-grid"]').should('be.visible');
    });

    it('should handle API errors gracefully', () => {
      // Intercept API to return error
      cy.intercept('GET', '/api/entities*', {
        statusCode: 500,
        body: { error: 'Internal Server Error' }
      }).as('getEntitiesError');

      // Apply a filter
      cy.get('[data-testid="entity-type-option"]').first().click();

      // Wait for API call
      cy.wait('@getEntitiesError');

      // Check that error state is shown
      cy.get('[data-testid="error-message"]').should('be.visible');
      cy.get('[data-testid="error-message"]').should('contain', 'Error');
      cy.get('[data-testid="retry-button"]').should('be.visible');
    });

    it('should retry failed API calls', () => {
      let callCount = 0;
      cy.intercept('GET', '/api/entities*', (req) => {
        callCount++;
        if (callCount === 1) {
          req.reply({ statusCode: 500, body: { error: 'Server Error' } });
        } else {
          req.reply({ fixture: 'entities-response.json' });
        }
      }).as('getEntitiesRetry');

      // Apply a filter
      cy.get('[data-testid="entity-type-option"]').first().click();

      // Wait for first (failed) call
      cy.wait('@getEntitiesRetry');

      // Check error state
      cy.get('[data-testid="error-message"]').should('be.visible');

      // Click retry button
      cy.get('[data-testid="retry-button"]').click();

      // Wait for second (successful) call
      cy.wait('@getEntitiesRetry');

      // Check that results are shown
      cy.get('[data-testid="results-grid"]').should('be.visible');
      cy.get('[data-testid="error-message"]').should('not.exist');
    });
  });

  describe('Search Debouncing', () => {
    it('should debounce search input to prevent excessive API calls', () => {
      let searchApiCalls = 0;
      cy.intercept('GET', '/api/entities*', (req) => {
        if (req.url.includes('searchTerm')) {
          searchApiCalls++;
        }
        req.reply({ fixture: 'entities-response.json' });
      }).as('getEntitiesSearch');

      // Type search query character by character
      const searchTerm = 'machine learning';
      cy.get('[data-testid="search-input"]').focus();
      
      for (let i = 0; i < searchTerm.length; i++) {
        cy.get('[data-testid="search-input"]').type(searchTerm[i]);
        cy.wait(50); // Small delay between characters
      }

      // Wait for debounce period
      cy.wait(1000);

      // Check that only one API call was made despite multiple character inputs
      cy.then(() => {
        expect(searchApiCalls).to.equal(1);
      });
    });

    it('should cancel previous search requests when new search is initiated', () => {
      // Intercept with different delays to simulate request cancellation
      let requestCount = 0;
      cy.intercept('GET', '/api/entities*', (req) => {
        requestCount++;
        const delay = requestCount === 1 ? 2000 : 100;
        req.reply({ delay, fixture: 'entities-response.json' });
      }).as('getEntitiesCancel');

      // Start first search
      cy.get('[data-testid="search-input"]').type('first search');
      
      // Immediately start second search
      cy.get('[data-testid="search-input"]').clear().type('second search');

      // Wait for requests
      cy.wait(1500);

      // Check that results correspond to the latest search
      cy.url().should('include', 'searchTerm=second%20search');
    });
  });

  describe('Filter State Management', () => {
    it('should maintain filter state during navigation', () => {
      // Apply filters
      cy.get('[data-testid="search-input"]').type('AI tools');
      cy.get('[data-testid="entity-type-option"]').first().click();
      cy.get('[data-testid="category-option"]').first().click();

      // Get current URL
      cy.url().then((currentUrl) => {
        // Navigate away and back
        cy.visit('/');
        cy.visit(currentUrl);

        // Check that filters are restored
        cy.get('[data-testid="search-input"]').should('have.value', 'AI tools');
        cy.get('[data-testid="active-filters"]').should('be.visible');
        cy.get('[data-testid="active-filters"]').should('contain', 'AI tools');
      });
    });

    it('should handle complex filter combinations efficiently', () => {
      // Monitor API calls
      let apiCallCount = 0;
      cy.intercept('GET', '/api/entities*', (req) => {
        apiCallCount++;
        req.reply({ fixture: 'entities-response.json' });
      }).as('getEntitiesComplex');

      // Apply many filters in sequence
      cy.get('[data-testid="search-input"]').type('machine learning');
      cy.get('[data-testid="entity-type-option"]').first().click();
      cy.get('[data-testid="category-option"]').first().click();
      cy.get('[data-testid="tag-option"]').first().click();
      
      // Open advanced filters
      cy.get('[data-testid="advanced-filters-toggle"]').click();
      cy.get('[data-testid="has-free-tier-checkbox"]').check();
      cy.get('[data-testid="api-access-checkbox"]').check();
      cy.get('[data-testid="rating-min-input"]').type('4');

      // Wait for all operations to complete
      cy.wait(2000);

      // Check that API calls were optimized
      cy.then(() => {
        expect(apiCallCount).to.be.lessThan(7); // Should be fewer than number of filter changes
      });

      // Check that all filters are applied
      cy.get('[data-testid="active-filters"]').should('contain', 'machine learning');
      cy.get('[data-testid="active-filters"]').should('contain', 'Free Tier');
      cy.get('[data-testid="active-filters"]').should('contain', 'Rating');
    });
  });

  describe('Memory and Resource Management', () => {
    it('should not cause memory leaks with repeated filter operations', () => {
      // Perform many filter operations
      for (let i = 0; i < 10; i++) {
        cy.get('[data-testid="search-input"]').clear().type(`search ${i}`);
        cy.get('[data-testid="entity-type-option"]').first().click();
        cy.get('[data-testid="entity-type-option"]').first().click(); // Toggle off
        cy.wait(100);
      }

      // Check that page is still responsive
      cy.get('[data-testid="search-input"]').should('be.visible');
      cy.get('[data-testid="results-grid"]').should('be.visible');
    });

    it('should handle large result sets efficiently', () => {
      // Intercept with large dataset
      cy.intercept('GET', '/api/entities*', { 
        fixture: 'large-entities-response.json' 
      }).as('getLargeEntities');

      // Apply filter that returns many results
      cy.get('[data-testid="search-input"]').type('AI');

      cy.wait('@getLargeEntities');

      // Check that page remains responsive
      cy.get('[data-testid="results-grid"]').should('be.visible');
      cy.get('[data-testid="resource-card"]').should('have.length.greaterThan', 20);

      // Test scrolling performance
      cy.scrollTo('bottom');
      cy.get('[data-testid="load-more-indicator"]').should('be.visible');
    });
  });

  describe('Cache Performance', () => {
    it('should cache filter results for improved performance', () => {
      let apiCallCount = 0;
      cy.intercept('GET', '/api/entities*', (req) => {
        apiCallCount++;
        req.reply({ fixture: 'entities-response.json' });
      }).as('getEntitiesCache');

      // Apply same filter twice
      cy.get('[data-testid="entity-type-option"]').first().click();
      cy.wait('@getEntitiesCache');

      // Clear and reapply same filter
      cy.get('[data-testid="clear-all-filters"]').click();
      cy.get('[data-testid="entity-type-option"]').first().click();

      // Wait a bit
      cy.wait(500);

      // Check that cache was used (fewer API calls)
      cy.then(() => {
        expect(apiCallCount).to.be.lessThan(3);
      });
    });

    it('should invalidate cache when appropriate', () => {
      let apiCallCount = 0;
      cy.intercept('GET', '/api/entities*', (req) => {
        apiCallCount++;
        req.reply({ fixture: 'entities-response.json' });
      }).as('getEntitiesInvalidate');

      // Apply filter
      cy.get('[data-testid="entity-type-option"]').first().click();
      cy.wait('@getEntitiesInvalidate');

      // Wait for cache to potentially expire (if implemented)
      cy.wait(5000);

      // Apply same filter again
      cy.get('[data-testid="clear-all-filters"]').click();
      cy.get('[data-testid="entity-type-option"]').first().click();
      cy.wait('@getEntitiesInvalidate');

      // Check that fresh API call was made
      cy.then(() => {
        expect(apiCallCount).to.equal(2);
      });
    });
  });

  describe('Performance Monitoring', () => {
    it('should display performance metrics when enabled', () => {
      // Enable performance monitoring (if available)
      cy.window().then((win) => {
        if (win.localStorage) {
          win.localStorage.setItem('showPerformanceMonitor', 'true');
        }
      });

      cy.reload();

      // Apply some filters to generate metrics
      cy.get('[data-testid="entity-type-option"]').first().click();
      cy.get('[data-testid="search-input"]').type('AI');

      // Check if performance monitor is visible
      cy.get('[data-testid="performance-monitor"]').should('be.visible');
      cy.get('[data-testid="api-call-count"]').should('be.visible');
      cy.get('[data-testid="response-time"]').should('be.visible');
    });
  });
});
