describe('Mobile Filter Responsiveness E2E Tests', () => {
  beforeEach(() => {
    // Set mobile viewport
    cy.viewport('iphone-x');
    cy.visit('/browse');
    
    // Wait for the page to load
    cy.get('[data-testid="browse-page"]', { timeout: 10000 }).should('be.visible');
  });

  describe('Mobile Filter UI', () => {
    it('should show mobile filter toggle button', () => {
      // Check that mobile filter button is visible
      cy.get('[data-testid="mobile-filter-toggle"]').should('be.visible');
      
      // Check that desktop sidebar is hidden
      cy.get('[data-testid="desktop-filter-sidebar"]').should('not.be.visible');
    });

    it('should open and close mobile filter sheet', () => {
      // Open mobile filter sheet
      cy.get('[data-testid="mobile-filter-toggle"]').click();
      cy.get('[data-testid="mobile-filter-sheet"]').should('be.visible');
      
      // Check that filter content is visible
      cy.get('[data-testid="filter-content"]').should('be.visible');
      
      // Close filter sheet
      cy.get('[data-testid="close-filter-sheet"]').click();
      cy.get('[data-testid="mobile-filter-sheet"]').should('not.be.visible');
    });

    it('should show filter count badge on mobile toggle', () => {
      // Apply some filters
      cy.get('[data-testid="mobile-filter-toggle"]').click();
      cy.get('[data-testid="entity-type-option"]').first().click();
      cy.get('[data-testid="category-option"]').first().click();
      cy.get('[data-testid="close-filter-sheet"]').click();
      
      // Check that badge shows correct count
      cy.get('[data-testid="mobile-filter-badge"]').should('contain', '2');
    });

    it('should handle touch interactions properly', () => {
      // Open mobile filter
      cy.get('[data-testid="mobile-filter-toggle"]').click();
      
      // Test touch scrolling in filter sheet
      cy.get('[data-testid="mobile-filter-sheet"]')
        .trigger('touchstart', { touches: [{ clientX: 100, clientY: 200 }] })
        .trigger('touchmove', { touches: [{ clientX: 100, clientY: 100 }] })
        .trigger('touchend');
      
      // Test filter selection with touch
      cy.get('[data-testid="entity-type-option"]').first()
        .trigger('touchstart')
        .trigger('touchend');
      
      // Check that filter is applied
      cy.get('[data-testid="active-filters"]').should('be.visible');
    });
  });

  describe('Mobile Search Experience', () => {
    it('should handle mobile search input properly', () => {
      // Test search input on mobile
      cy.get('[data-testid="search-input"]')
        .should('be.visible')
        .type('AI tools');
      
      // Check that virtual keyboard doesn't break layout
      cy.get('[data-testid="search-input"]').should('have.value', 'AI tools');
      cy.get('[data-testid="results-grid"]').should('be.visible');
    });

    it('should show mobile-optimized search suggestions', () => {
      // Type in search to trigger suggestions
      cy.get('[data-testid="search-input"]').type('AI');
      
      // Check that suggestions are mobile-friendly
      cy.get('[data-testid="search-suggestions"]').should('be.visible');
      cy.get('[data-testid="search-suggestion-item"]').should('have.css', 'min-height');
    });
  });

  describe('Mobile Results Display', () => {
    it('should display results in mobile-optimized grid', () => {
      // Check that results use single column on mobile
      cy.get('[data-testid="results-grid"]').should('have.class', 'grid-cols-1');
      
      // Check that cards are mobile-optimized
      cy.get('[data-testid="resource-card"]').first().should('be.visible');
      cy.get('[data-testid="resource-card"]').first().should('have.css', 'width');
    });

    it('should handle infinite scroll on mobile', () => {
      // Scroll to bottom to trigger load more
      cy.scrollTo('bottom');
      
      // Check that more results load
      cy.get('[data-testid="load-more-indicator"]').should('be.visible');
      cy.get('[data-testid="resource-card"]').should('have.length.greaterThan', 10);
    });
  });

  describe('Mobile Filter Performance', () => {
    it('should maintain smooth performance on mobile', () => {
      // Open mobile filters
      cy.get('[data-testid="mobile-filter-toggle"]').click();
      
      // Apply multiple filters quickly
      cy.get('[data-testid="entity-type-option"]').first().click();
      cy.get('[data-testid="category-option"]').first().click();
      cy.get('[data-testid="tag-option"]').first().click();
      
      // Check that UI remains responsive
      cy.get('[data-testid="active-filters"]').should('be.visible');
      cy.get('[data-testid="results-grid"]').should('be.visible');
    });

    it('should handle orientation changes', () => {
      // Start in portrait
      cy.viewport('iphone-x');
      cy.get('[data-testid="mobile-filter-toggle"]').should('be.visible');
      
      // Change to landscape
      cy.viewport(812, 375); // iPhone X landscape
      cy.get('[data-testid="mobile-filter-toggle"]').should('be.visible');
      
      // Check that layout adapts
      cy.get('[data-testid="results-grid"]').should('be.visible');
    });
  });

  describe('Tablet Responsiveness', () => {
    beforeEach(() => {
      // Set tablet viewport
      cy.viewport('ipad-2');
    });

    it('should show appropriate layout for tablet', () => {
      // Check that tablet shows hybrid layout
      cy.get('[data-testid="filter-sidebar"]').should('be.visible');
      cy.get('[data-testid="results-grid"]').should('have.class', 'md:grid-cols-2');
    });

    it('should handle tablet touch interactions', () => {
      // Test filter interactions on tablet
      cy.get('[data-testid="entity-type-option"]').first().click();
      cy.get('[data-testid="active-filters"]').should('be.visible');
      
      // Test advanced filters on tablet
      cy.get('[data-testid="advanced-filters-toggle"]').click();
      cy.get('[data-testid="advanced-filters"]').should('be.visible');
    });
  });
});
