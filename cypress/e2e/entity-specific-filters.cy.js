describe('Entity-Specific Filters E2E Tests', () => {
  beforeEach(() => {
    cy.visit('/browse');
    cy.get('[data-testid="browse-page"]', { timeout: 10000 }).should('be.visible');
  });

  describe('Course Filters', () => {
    beforeEach(() => {
      // Select Course entity type to reveal course-specific filters
      cy.get('[data-testid="entity-type-course"]').click();
      cy.get('[data-testid="entity-specific-filters"]').should('be.visible');
      cy.get('[data-testid="course-filters"]').should('be.visible');
    });

    it('should apply skill level filters', () => {
      // Test skill level multiselect
      cy.get('[data-testid="skill-level-beginner"]').click();
      cy.get('[data-testid="skill-level-intermediate"]').click();
      
      // Check URL parameters
      cy.url().should('include', 'entity_type_filters');
      cy.url().should('include', 'skill_levels');
      
      // Check active filters
      cy.get('[data-testid="active-filters"]').should('contain', 'Type-Specific');
    });

    it('should apply certificate availability filter', () => {
      cy.get('[data-testid="certificate-available"]').check();
      
      // Check that filter is applied
      cy.url().should('include', 'certificate_available=true');
      cy.get('[data-testid="active-filters"]').should('be.visible');
    });

    it('should apply instructor name filter', () => {
      const instructorName = 'Dr. Smith';
      cy.get('[data-testid="instructor-name-input"]').type(instructorName);
      
      // Check URL parameters
      cy.url().should('include', 'instructor_name');
      cy.get('[data-testid="active-filters"]').should('be.visible');
    });

    it('should apply duration filter', () => {
      const duration = '10 hours';
      cy.get('[data-testid="duration-input"]').type(duration);
      
      cy.url().should('include', 'duration_text');
    });

    it('should apply enrollment range filters', () => {
      cy.get('[data-testid="enrollment-min-input"]').type('100');
      cy.get('[data-testid="enrollment-max-input"]').type('10000');
      
      cy.url().should('include', 'enrollment_min=100');
      cy.url().should('include', 'enrollment_max=10000');
    });

    it('should apply prerequisites filter', () => {
      const prerequisites = 'Basic programming knowledge';
      cy.get('[data-testid="prerequisites-input"]').type(prerequisites);
      
      cy.url().should('include', 'prerequisites');
    });

    it('should apply syllabus availability filter', () => {
      cy.get('[data-testid="has-syllabus"]').check();
      
      cy.url().should('include', 'has_syllabus=true');
    });
  });

  describe('Job Filters', () => {
    beforeEach(() => {
      cy.get('[data-testid="entity-type-job"]').click();
      cy.get('[data-testid="job-filters"]').should('be.visible');
    });

    it('should apply employment type filters', () => {
      cy.get('[data-testid="employment-type-fulltime"]').click();
      cy.get('[data-testid="employment-type-remote"]').click();
      
      cy.url().should('include', 'employment_types');
      cy.get('[data-testid="active-filters"]').should('contain', 'Type-Specific');
    });

    it('should apply experience level filters', () => {
      cy.get('[data-testid="experience-level-mid"]').click();
      cy.get('[data-testid="experience-level-senior"]').click();
      
      cy.url().should('include', 'experience_levels');
    });

    it('should apply location type filters', () => {
      cy.get('[data-testid="location-type-remote"]').click();
      cy.get('[data-testid="location-type-hybrid"]').click();
      
      cy.url().should('include', 'location_types');
    });

    it('should apply company name filter', () => {
      const companyName = 'Google';
      cy.get('[data-testid="company-name-input"]').type(companyName);
      
      cy.url().should('include', 'company_name');
    });

    it('should apply job title filter', () => {
      const jobTitle = 'AI Engineer';
      cy.get('[data-testid="job-title-input"]').type(jobTitle);
      
      cy.url().should('include', 'job_title');
    });

    it('should apply salary range filters', () => {
      cy.get('[data-testid="salary-min-input"]').type('80');
      cy.get('[data-testid="salary-max-input"]').type('150');
      
      cy.url().should('include', 'salary_min=80');
      cy.url().should('include', 'salary_max=150');
    });

    it('should apply job description filter', () => {
      const description = 'machine learning';
      cy.get('[data-testid="job-description-input"]').type(description);
      
      cy.url().should('include', 'job_description');
    });

    it('should apply application URL availability filter', () => {
      cy.get('[data-testid="has-application-url"]').check();
      
      cy.url().should('include', 'has_application_url=true');
    });
  });

  describe('Hardware Filters', () => {
    beforeEach(() => {
      cy.get('[data-testid="entity-type-hardware"]').click();
      cy.get('[data-testid="hardware-filters"]').should('be.visible');
    });

    it('should apply hardware type filters', () => {
      cy.get('[data-testid="hardware-type-gpu"]').click();
      cy.get('[data-testid="hardware-type-cpu"]').click();
      
      cy.url().should('include', 'hardware_types');
    });

    it('should apply manufacturer filters', () => {
      cy.get('[data-testid="manufacturer-nvidia"]').click();
      cy.get('[data-testid="manufacturer-intel"]').click();
      
      cy.url().should('include', 'manufacturers');
    });

    it('should apply release date range filters', () => {
      cy.get('[data-testid="release-date-from"]').type('2023-01-01');
      cy.get('[data-testid="release-date-to"]').type('2024-12-31');
      
      cy.url().should('include', 'release_date_from=2023-01-01');
      cy.url().should('include', 'release_date_to=2024-12-31');
    });

    it('should apply price range filters', () => {
      cy.get('[data-testid="price-min-input"]').type('500');
      cy.get('[data-testid="price-max-input"]').type('2000');
      
      cy.url().should('include', 'price_min=500');
      cy.url().should('include', 'price_max=2000');
    });

    it('should apply specifications search filter', () => {
      const specs = 'GDDR6';
      cy.get('[data-testid="specifications-search-input"]').type(specs);
      
      cy.url().should('include', 'specifications_search');
    });

    it('should apply datasheet availability filter', () => {
      cy.get('[data-testid="has-datasheet"]').check();
      
      cy.url().should('include', 'has_datasheet=true');
    });

    it('should apply memory search filter', () => {
      const memory = '16GB';
      cy.get('[data-testid="memory-search-input"]').type(memory);
      
      cy.url().should('include', 'memory_search');
    });

    it('should apply processor search filter', () => {
      const processor = 'Intel i9';
      cy.get('[data-testid="processor-search-input"]').type(processor);
      
      cy.url().should('include', 'processor_search');
    });
  });

  describe('Event Filters', () => {
    beforeEach(() => {
      cy.get('[data-testid="entity-type-event"]').click();
      cy.get('[data-testid="event-filters"]').should('be.visible');
    });

    it('should apply event type filters', () => {
      cy.get('[data-testid="event-type-conference"]').click();
      cy.get('[data-testid="event-type-workshop"]').click();
      
      cy.url().should('include', 'event_types');
    });

    it('should apply date range filters', () => {
      cy.get('[data-testid="start-date-from"]').type('2024-01-01');
      cy.get('[data-testid="start-date-to"]').type('2024-12-31');
      cy.get('[data-testid="end-date-from"]').type('2024-01-01');
      cy.get('[data-testid="end-date-to"]').type('2024-12-31');
      
      cy.url().should('include', 'start_date_from=2024-01-01');
      cy.url().should('include', 'start_date_to=2024-12-31');
      cy.url().should('include', 'end_date_from=2024-01-01');
      cy.url().should('include', 'end_date_to=2024-12-31');
    });

    it('should apply online event filter', () => {
      cy.get('[data-testid="is-online"]').check();
      
      cy.url().should('include', 'is_online=true');
    });

    it('should apply location filter', () => {
      const location = 'San Francisco';
      cy.get('[data-testid="location-input"]').type(location);
      
      cy.url().should('include', 'location');
    });

    it('should apply price text filter', () => {
      const price = 'Free';
      cy.get('[data-testid="price-text-input"]').type(price);
      
      cy.url().should('include', 'price_text');
    });

    it('should apply registration filters', () => {
      cy.get('[data-testid="registration-required"]').check();
      cy.get('[data-testid="has-registration-url"]').check();
      
      cy.url().should('include', 'registration_required=true');
      cy.url().should('include', 'has_registration_url=true');
    });

    it('should apply speakers search filter', () => {
      const speakers = 'Elon Musk';
      cy.get('[data-testid="speakers-search-input"]').type(speakers);
      
      cy.url().should('include', 'speakers_search');
    });

    it('should apply target audience search filter', () => {
      const audience = 'developers';
      cy.get('[data-testid="target-audience-search-input"]').type(audience);
      
      cy.url().should('include', 'target_audience_search');
    });
  });

  describe('Tool Filters', () => {
    beforeEach(() => {
      cy.get('[data-testid="entity-type-tool"]').click();
      cy.get('[data-testid="tool-filters"]').should('be.visible');
    });

    it('should apply technical level filters', () => {
      cy.get('[data-testid="technical-level-beginner"]').click();
      cy.get('[data-testid="technical-level-intermediate"]').click();
      
      cy.url().should('include', 'technical_levels');
    });

    it('should apply learning curve filters', () => {
      cy.get('[data-testid="learning-curve-low"]').click();
      cy.get('[data-testid="learning-curve-medium"]').click();
      
      cy.url().should('include', 'learning_curves');
    });

    it('should apply pricing model filters', () => {
      cy.get('[data-testid="pricing-model-free"]').click();
      cy.get('[data-testid="pricing-model-freemium"]').click();
      
      cy.url().should('include', 'pricing_models');
    });

    it('should apply feature availability filters', () => {
      cy.get('[data-testid="has-api"]').check();
      cy.get('[data-testid="has-free-tier"]').check();
      cy.get('[data-testid="open-source"]').check();
      cy.get('[data-testid="mobile-support"]').check();
      cy.get('[data-testid="demo-available"]').check();
      
      cy.url().should('include', 'has_api=true');
      cy.url().should('include', 'has_free_tier=true');
      cy.url().should('include', 'open_source=true');
      cy.url().should('include', 'mobile_support=true');
      cy.url().should('include', 'demo_available=true');
    });

    it('should apply platform filters', () => {
      cy.get('[data-testid="platform-web"]').click();
      cy.get('[data-testid="platform-ios"]').click();
      
      cy.url().should('include', 'platforms');
    });

    it('should apply integration filters', () => {
      cy.get('[data-testid="integration-slack"]').click();
      cy.get('[data-testid="integration-discord"]').click();
      
      cy.url().should('include', 'integrations');
    });

    it('should apply framework filters', () => {
      cy.get('[data-testid="framework-tensorflow"]').click();
      cy.get('[data-testid="framework-pytorch"]').click();
      
      cy.url().should('include', 'frameworks');
    });

    it('should apply search filters', () => {
      const keyFeatures = 'natural language processing';
      const useCases = 'content generation';
      const targetAudience = 'developers';
      
      cy.get('[data-testid="key-features-search-input"]').type(keyFeatures);
      cy.get('[data-testid="use-cases-search-input"]').type(useCases);
      cy.get('[data-testid="target-audience-search-input"]').type(targetAudience);
      
      cy.url().should('include', 'key_features_search');
      cy.url().should('include', 'use_cases_search');
      cy.url().should('include', 'target_audience_search');
    });
  });

  describe('Multiple Entity Type Filters', () => {
    it('should handle multiple entity types with different filter sets', () => {
      // Select multiple entity types
      cy.get('[data-testid="entity-type-course"]').click();
      cy.get('[data-testid="entity-type-job"]').click();
      cy.get('[data-testid="entity-type-tool"]').click();
      
      // Check that all filter sets are visible
      cy.get('[data-testid="course-filters"]').should('be.visible');
      cy.get('[data-testid="job-filters"]').should('be.visible');
      cy.get('[data-testid="tool-filters"]').should('be.visible');
      
      // Apply filters for each type
      cy.get('[data-testid="skill-level-beginner"]').click(); // Course
      cy.get('[data-testid="employment-type-fulltime"]').click(); // Job
      cy.get('[data-testid="technical-level-intermediate"]').click(); // Tool
      
      // Check that all filters are in URL
      cy.url().should('include', 'entity_type_filters');
      cy.get('[data-testid="active-filters"]').should('contain', 'Type-Specific');
    });

    it('should clear entity-specific filters when entity type is deselected', () => {
      // Select entity type and apply filters
      cy.get('[data-testid="entity-type-course"]').click();
      cy.get('[data-testid="skill-level-beginner"]').click();
      
      // Verify filter is applied
      cy.url().should('include', 'entity_type_filters');
      
      // Deselect entity type
      cy.get('[data-testid="entity-type-course"]').click();
      
      // Check that entity-specific filters are cleared
      cy.get('[data-testid="course-filters"]').should('not.exist');
      // Note: URL might still contain the parameter until next API call
    });
  });
});
