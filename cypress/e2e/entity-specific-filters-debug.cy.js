/**
 * E2E test to debug and reproduce the entity_type_filters.property issue
 */

describe('Entity-Specific Filters Debug', () => {
  beforeEach(() => {
    // Visit the browse page
    cy.visit('/browse');
    
    // Wait for the page to load
    cy.get('[data-testid="browse-page"]', { timeout: 10000 }).should('be.visible');
    
    // Intercept the entities API call to capture the request
    cy.intercept('GET', '**/entities*', (req) => {
      console.log('🔍 Intercepted entities API call');
      console.log('📋 Query params:', req.query);
      console.log('📊 entity_type_filters param:', req.query.entity_type_filters);
      
      if (req.query.entity_type_filters) {
        try {
          const parsed = JSON.parse(req.query.entity_type_filters);
          console.log('🔍 Parsed entity_type_filters:', parsed);
          console.log('🔑 Keys in parsed filters:', Object.keys(parsed));
        } catch (e) {
          console.error('❌ Failed to parse entity_type_filters:', e);
        }
      }
    }).as('getEntities');
  });

  it('should reproduce the entity_type_filters.property issue', () => {
    // Step 1: Select an entity type that has specific filters (AI Tool)
    cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
    
    // Wait for the entity-specific filters to appear
    cy.get('[data-testid="entity-specific-filters"]', { timeout: 5000 }).should('be.visible');
    
    // Step 2: Try to apply a filter (this should trigger the issue)
    cy.get('[data-testid="technical-levels-filter"]').should('be.visible');
    cy.get('[data-testid="technical-level-beginner"]').click();
    
    // Step 3: Check the console for debugging information
    cy.window().then((win) => {
      // Add a custom command to capture console logs
      const logs = [];
      const originalLog = win.console.log;
      win.console.log = (...args) => {
        logs.push(args.join(' '));
        originalLog.apply(win.console, args);
      };
      
      // Store logs for later inspection
      cy.wrap(logs).as('consoleLogs');
    });
    
    // Step 4: Wait for the API call and check if it fails
    cy.wait('@getEntities', { timeout: 10000 }).then((interception) => {
      const { query } = interception.request;
      
      if (query.entity_type_filters) {
        try {
          const parsed = JSON.parse(query.entity_type_filters);
          console.log('✅ Successfully parsed entity_type_filters:', parsed);
          
          // Check if there's a 'property' key (this would be the bug)
          if ('property' in parsed) {
            console.error('❌ FOUND THE BUG: "property" key exists in entity_type_filters');
            console.error('🐛 Problematic structure:', parsed);
          } else {
            console.log('✅ No "property" key found - structure looks correct');
          }
          
          // Check if entity type names are used as keys
          const expectedKeys = ['AI Tool', 'Course', 'Job', 'Hardware', 'Event'];
          const actualKeys = Object.keys(parsed);
          const validKeys = actualKeys.filter(key => expectedKeys.includes(key));
          
          if (validKeys.length > 0) {
            console.log('✅ Found valid entity type keys:', validKeys);
          } else {
            console.error('❌ No valid entity type keys found. Actual keys:', actualKeys);
          }
          
        } catch (e) {
          console.error('❌ Failed to parse entity_type_filters from API call:', e);
        }
      }
    });
  });

  it('should test multiple entity types and filters', () => {
    // Test with multiple entity types to see if the issue is consistent
    const entityTypes = [
      { name: 'AI Tool', testId: 'ai-tool', filterTestId: 'technical-level-beginner' },
      { name: 'Course', testId: 'course', filterTestId: 'skill-level-intermediate' },
      { name: 'Job', testId: 'job', filterTestId: 'employment-type-fulltime' }
    ];

    entityTypes.forEach((entityType, index) => {
      cy.log(`Testing entity type: ${entityType.name}`);
      
      // Select the entity type
      cy.get('[data-testid="entity-type-filter"]').contains(entityType.name).click();
      
      // Wait a bit for the filters to load
      cy.wait(1000);
      
      // Try to apply a filter if it exists
      cy.get('body').then(($body) => {
        if ($body.find(`[data-testid="${entityType.filterTestId}"]`).length > 0) {
          cy.get(`[data-testid="${entityType.filterTestId}"]`).click();
          
          // Wait for the API call
          cy.wait('@getEntities', { timeout: 5000 }).then((interception) => {
            const { query } = interception.request;
            
            if (query.entity_type_filters) {
              try {
                const parsed = JSON.parse(query.entity_type_filters);
                console.log(`📊 ${entityType.name} filters:`, parsed);
                
                // Check for the bug
                if ('property' in parsed) {
                  console.error(`❌ BUG FOUND in ${entityType.name}: "property" key exists`);
                }
              } catch (e) {
                console.error(`❌ Parse error for ${entityType.name}:`, e);
              }
            }
          });
        } else {
          cy.log(`⚠️ No filter found for ${entityType.name}`);
        }
      });
    });
  });

  it('should capture and analyze the complete data flow', () => {
    // Enable comprehensive logging
    cy.window().then((win) => {
      // Override console methods to capture all logs
      const logs = {
        log: [],
        warn: [],
        error: []
      };
      
      const originalLog = win.console.log;
      const originalWarn = win.console.warn;
      const originalError = win.console.error;
      
      win.console.log = (...args) => {
        logs.log.push(args.join(' '));
        originalLog.apply(win.console, args);
      };
      
      win.console.warn = (...args) => {
        logs.warn.push(args.join(' '));
        originalWarn.apply(win.console, args);
      };
      
      win.console.error = (...args) => {
        logs.error.push(args.join(' '));
        originalError.apply(win.console, args);
      };
      
      cy.wrap(logs).as('allLogs');
    });
    
    // Perform the filter action
    cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
    cy.wait(1000);
    
    // Try to apply a filter
    cy.get('body').then(($body) => {
      if ($body.find('[data-testid="technical-level-beginner"]').length > 0) {
        cy.get('[data-testid="technical-level-beginner"]').click();
      }
    });
    
    // Wait for processing
    cy.wait(2000);
    
    // Analyze the logs
    cy.get('@allLogs').then((logs) => {
      console.log('📋 All captured logs:');
      console.log('🔍 Regular logs:', logs.log);
      console.log('⚠️ Warnings:', logs.warn);
      console.log('❌ Errors:', logs.error);
      
      // Look for specific patterns that might indicate the issue
      const relevantLogs = logs.log.filter(log => 
        log.includes('entity_type_filters') || 
        log.includes('EntitySpecificFilters') ||
        log.includes('handleAdvancedFilterChange')
      );
      
      console.log('🎯 Relevant logs:', relevantLogs);
    });
  });
});
