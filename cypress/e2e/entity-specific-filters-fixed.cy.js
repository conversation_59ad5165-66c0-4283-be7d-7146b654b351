/**
 * E2E test to verify the entity-specific filters fix works correctly
 * Tests the conversion from frontend display names to backend keys
 */

describe('Entity-Specific Filters - Fixed Implementation', () => {
  beforeEach(() => {
    // Visit the browse page
    cy.visit('/browse');
    
    // Wait for the page to load
    cy.get('[data-testid="browse-page"]', { timeout: 10000 }).should('be.visible');
    
    // Intercept the entities API call to verify the correct structure is sent
    cy.intercept('GET', '**/entities*', (req) => {
      console.log('🔍 Intercepted entities API call');
      console.log('📋 Query params:', req.query);
      
      if (req.query.entity_type_filters) {
        try {
          const parsed = JSON.parse(req.query.entity_type_filters);
          console.log('✅ Parsed entity_type_filters:', parsed);
          console.log('🔑 Keys in parsed filters:', Object.keys(parsed));
          
          // Verify that we're using backend keys, not frontend display names
          const expectedBackendKeys = ['tool', 'course', 'job', 'hardware', 'event'];
          const actualKeys = Object.keys(parsed);
          const hasValidBackendKeys = actualKeys.some(key => expectedBackendKeys.includes(key));
          const hasInvalidFrontendKeys = actualKeys.some(key => ['AI Tool', 'Course', 'Job'].includes(key));
          
          if (hasValidBackendKeys && !hasInvalidFrontendKeys) {
            console.log('✅ SUCCESS: Using correct backend keys');
          } else {
            console.error('❌ FAILURE: Still using frontend display names or invalid keys');
          }
          
          // Check for the problematic 'property' field
          if ('property' in parsed) {
            console.error('❌ CRITICAL: "property" field still exists!');
          } else {
            console.log('✅ SUCCESS: No "property" field found');
          }
          
        } catch (e) {
          console.error('❌ Failed to parse entity_type_filters:', e);
        }
      }
    }).as('getEntities');
  });

  it('should convert AI Tool filters to backend format', () => {
    // Select AI Tool entity type
    cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
    
    // Wait for entity-specific filters to appear
    cy.get('[data-testid="entity-specific-filters"]', { timeout: 5000 }).should('be.visible');
    
    // Apply a technical level filter
    cy.get('[data-testid="technical-level-beginner"]').should('be.visible').click();
    
    // Wait for the API call and verify the structure
    cy.wait('@getEntities', { timeout: 10000 }).then((interception) => {
      const { query } = interception.request;
      
      if (query.entity_type_filters) {
        const parsed = JSON.parse(query.entity_type_filters);
        
        // Should have 'tool' key, not 'AI Tool'
        expect(parsed).to.have.property('tool');
        expect(parsed).to.not.have.property('AI Tool');
        expect(parsed).to.not.have.property('property');
        
        // Should have the correct filter structure
        expect(parsed.tool).to.have.property('technical_levels');
        expect(parsed.tool.technical_levels).to.include('BEGINNER');
      }
    });
  });

  it('should convert Course filters to backend format', () => {
    // Select Course entity type
    cy.get('[data-testid="entity-type-filter"]').contains('Course').click();
    
    // Wait for entity-specific filters to appear
    cy.get('[data-testid="entity-specific-filters"]', { timeout: 5000 }).should('be.visible');
    
    // Apply a skill level filter
    cy.get('[data-testid="skill-level-intermediate"]').should('be.visible').click();
    
    // Wait for the API call and verify the structure
    cy.wait('@getEntities', { timeout: 10000 }).then((interception) => {
      const { query } = interception.request;
      
      if (query.entity_type_filters) {
        const parsed = JSON.parse(query.entity_type_filters);
        
        // Should have 'course' key, not 'Course'
        expect(parsed).to.have.property('course');
        expect(parsed).to.not.have.property('Course');
        expect(parsed).to.not.have.property('property');
        
        // Should have the correct filter structure
        expect(parsed.course).to.have.property('skill_levels');
        expect(parsed.course.skill_levels).to.include('INTERMEDIATE');
      }
    });
  });

  it('should handle multiple entity types correctly', () => {
    // Select multiple entity types
    cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
    cy.get('[data-testid="entity-type-filter"]').contains('Course').click();
    
    // Wait for entity-specific filters to appear
    cy.get('[data-testid="entity-specific-filters"]', { timeout: 5000 }).should('be.visible');
    
    // Apply filters for both entity types
    cy.get('[data-testid="technical-level-beginner"]').should('be.visible').click();
    cy.get('[data-testid="skill-level-advanced"]').should('be.visible').click();
    
    // Wait for the API call and verify the structure
    cy.wait('@getEntities', { timeout: 10000 }).then((interception) => {
      const { query } = interception.request;
      
      if (query.entity_type_filters) {
        const parsed = JSON.parse(query.entity_type_filters);
        
        // Should have both backend keys
        expect(parsed).to.have.property('tool');
        expect(parsed).to.have.property('course');
        
        // Should not have frontend display names
        expect(parsed).to.not.have.property('AI Tool');
        expect(parsed).to.not.have.property('Course');
        expect(parsed).to.not.have.property('property');
        
        // Should have the correct filter structures
        expect(parsed.tool.technical_levels).to.include('BEGINNER');
        expect(parsed.course.skill_levels).to.include('ADVANCED');
      }
    });
  });

  it('should handle Job filters correctly', () => {
    // Select Job entity type
    cy.get('[data-testid="entity-type-filter"]').contains('Job').click();
    
    // Wait for entity-specific filters to appear
    cy.get('[data-testid="entity-specific-filters"]', { timeout: 5000 }).should('be.visible');
    
    // Apply an employment type filter
    cy.get('[data-testid="employment-type-fulltime"]').should('be.visible').click();
    
    // Wait for the API call and verify the structure
    cy.wait('@getEntities', { timeout: 10000 }).then((interception) => {
      const { query } = interception.request;
      
      if (query.entity_type_filters) {
        const parsed = JSON.parse(query.entity_type_filters);
        
        // Should have 'job' key, not 'Job'
        expect(parsed).to.have.property('job');
        expect(parsed).to.not.have.property('Job');
        expect(parsed).to.not.have.property('property');
        
        // Should have the correct filter structure
        expect(parsed.job).to.have.property('employment_types');
        expect(parsed.job.employment_types).to.include('Full-time');
      }
    });
  });

  it('should not send any filters when none are selected', () => {
    // Select an entity type but don't apply any filters
    cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
    
    // Wait for the initial API call (without filters)
    cy.wait('@getEntities', { timeout: 10000 }).then((interception) => {
      const { query } = interception.request;
      
      // Should not have entity_type_filters parameter when no filters are applied
      expect(query).to.not.have.property('entity_type_filters');
    });
  });

  it('should handle filter removal correctly', () => {
    // Select AI Tool and apply a filter
    cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
    cy.get('[data-testid="technical-level-beginner"]').should('be.visible').click();
    
    // Wait for the API call with filters
    cy.wait('@getEntities', { timeout: 10000 });
    
    // Remove the filter by clicking it again
    cy.get('[data-testid="technical-level-beginner"]').click();
    
    // Wait for the API call without filters
    cy.wait('@getEntities', { timeout: 10000 }).then((interception) => {
      const { query } = interception.request;
      
      // Should not have entity_type_filters when all filters are removed
      expect(query).to.not.have.property('entity_type_filters');
    });
  });

  it('should handle boolean filters correctly', () => {
    // Select AI Tool entity type
    cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
    
    // Wait for entity-specific filters to appear
    cy.get('[data-testid="entity-specific-filters"]', { timeout: 5000 }).should('be.visible');
    
    // Apply a boolean filter (e.g., "Has API")
    cy.get('[data-testid="has-api-checkbox"]').should('be.visible').click();
    
    // Wait for the API call and verify the structure
    cy.wait('@getEntities', { timeout: 10000 }).then((interception) => {
      const { query } = interception.request;
      
      if (query.entity_type_filters) {
        const parsed = JSON.parse(query.entity_type_filters);
        
        // Should have 'tool' key with boolean filter
        expect(parsed).to.have.property('tool');
        expect(parsed.tool).to.have.property('has_api');
        expect(parsed.tool.has_api).to.be.true;
      }
    });
  });

  it('should handle text filters correctly', () => {
    // Select AI Tool entity type
    cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
    
    // Wait for entity-specific filters to appear
    cy.get('[data-testid="entity-specific-filters"]', { timeout: 5000 }).should('be.visible');
    
    // Apply a text filter
    cy.get('[data-testid="key-features-search"]').should('be.visible').type('machine learning');
    
    // Wait for the API call and verify the structure
    cy.wait('@getEntities', { timeout: 10000 }).then((interception) => {
      const { query } = interception.request;
      
      if (query.entity_type_filters) {
        const parsed = JSON.parse(query.entity_type_filters);
        
        // Should have 'tool' key with text filter
        expect(parsed).to.have.property('tool');
        expect(parsed.tool).to.have.property('key_features_search');
        expect(parsed.tool.key_features_search).to.equal('machine learning');
      }
    });
  });
});
